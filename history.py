import requests
import json
from pymongo import MongoClient
from datetime import datetime, timedelta
import threading
import queue
from tqdm import tqdm
import time
import random
from collections import defaultdict
import sys
import logging
import os
from statistics import mean, median

# Set up minimal logging - only file logging for errors
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, "sales_history.log")
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB Configuration
MONGODB_URI = "*******************************************************************"
MONGODB_DB = "test"
SALES_COLLECTION = "saleshistories"
CATALOG_COLLECTION = "catalog"

# Configuration
NUM_THREADS = 500
CATEGORIES = [1, 2, 3, 13, 16, 17, 19, 20, 21, 23, 24, 25, 26, 27, 28, 36, 37, 38, 47, 48, 53, 54, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81]
WAIT_TIME = 6 * 60 * 60  # 6 hours
MAX_RETRIES = 3
REQUESTS_PER_PROXY = 350

# Proxy configuration
PROXY_URL = "https://proxy.webshare.io/api/v2/proxy/list/download/mppaungsmfcdtflcalmqswojioauylnrbmfjimqd/-/any/username/direct/-/"
proxies = []
proxy_lock = threading.Lock()

# Track proxy status
proxy_status = {}
RATE_LIMIT_DURATION = 120  # 60 seconds cooldown for rate limited proxies

class ProxyStatus:
    def __init__(self):
        self.requests = 0
        self.rate_limited_until = 0
        self.last_used = 0
        self.errors = 0

def load_proxies():
    try:
        response = requests.get(PROXY_URL)
        response.raise_for_status()
        proxy_list = []
        for line in response.text.splitlines():
            if not line.strip():
                continue
            try:
                parts = line.strip().split(':')
                if len(parts) == 4:
                    ip, port, username, password = parts
                    proxy = {
                        'http': f"http://{username}:{password}@{ip}:{port}",
                        'https': f"http://{username}:{password}@{ip}:{port}"
                    }
                    proxy_list.append(proxy)
                    # Initialize proxy status
                    proxy_status[proxy['http']] = ProxyStatus()
            except Exception:
                continue
        print(f"Loaded {len(proxy_list)} proxies")
        return proxy_list
    except Exception:
        print("Failed to load proxies")
        return []

def get_available_proxy():
    current_time = time.time()
    with proxy_lock:
        # Filter available proxies
        available = [
            p for p in proxies
            if (proxy_status[p['http']].requests < REQUESTS_PER_PROXY and
                proxy_status[p['http']].rate_limited_until <= current_time and
                proxy_status[p['http']].errors < 5)  # Skip proxies with too many errors
        ]
        
        if not available:
            # Reset all proxies that have cooled down
            for proxy in proxies:
                status = proxy_status[proxy['http']]
                if current_time - status.rate_limited_until >= RATE_LIMIT_DURATION:
                    status.requests = 0
                    status.rate_limited_until = 0
                    status.errors = 0
            
            # Try again
            available = [
                p for p in proxies
                if (proxy_status[p['http']].requests < REQUESTS_PER_PROXY and
                    proxy_status[p['http']].rate_limited_until <= current_time and
                    proxy_status[p['http']].errors < 5)
            ]
            
            if not available:
                return None
        
        # Sort by least recently used and least used
        proxy = min(available, key=lambda p: (
            proxy_status[p['http']].last_used,
            proxy_status[p['http']].requests
        ))
        
        # Update proxy status
        status = proxy_status[proxy['http']]
        status.requests += 1
        status.last_used = current_time
        
        return proxy

def mark_proxy_rate_limited(proxy):
    with proxy_lock:
        status = proxy_status[proxy['http']]
        status.rate_limited_until = time.time() + RATE_LIMIT_DURATION
        status.requests = REQUESTS_PER_PROXY

def mark_proxy_error(proxy):
    with proxy_lock:
        proxy_status[proxy['http']].errors += 1

def calculate_sales_statistics(sales_data):
    """
    Calculate sales statistics from the provided sales data.
    
    Args:
        sales_data (list): List of sales data objects
        
    Returns:
        dict: Dictionary containing sales statistics
    """
    # Initialize statistics dictionary
    stats = {
        "last24Hours": {
            "total": 0,
            "byVariant": {}
        },
        "priceAnalysis": {
            "trend": "stable",
            "percentChange": 0.0
        },
        "byVariant": {}
    }
    
    # Get current time for 24-hour calculation
    now = datetime.now()
    last_24_hours = now - timedelta(hours=24)
    
    # Group sales by variant
    sales_by_variant = defaultdict(list)
    
    # Process each sale
    for sale in sales_data:
        # Extract variant (default to "Normal" if not specified)
        variant = sale.get("variant", "Normal")
        
        # Calculate total price (purchase + shipping)
        total_price = sale.get("purchasePrice", 0) + sale.get("shippingPrice", 0)
        
        # Parse order date
        try:
            # Parse the date and convert to offset-naive by replacing timezone with UTC
            # and then removing the timezone info
            order_date_str = sale.get("orderDate").replace("Z", "+00:00")
            order_date = datetime.fromisoformat(order_date_str)
            # Convert to offset-naive datetime for comparison with now()
            order_date = order_date.replace(tzinfo=None)
        except (ValueError, AttributeError):
            # Skip this sale if date parsing fails
            continue
        
        # Add to variant-specific list
        sales_by_variant[variant].append({
            "date": order_date,
            "price": total_price,
            "condition": sale.get("condition")
        })
        
        # Check if sale was in the last 24 hours
        if order_date >= last_24_hours:
            stats["last24Hours"]["total"] += 1
            
            # Increment variant-specific 24-hour count
            if variant not in stats["last24Hours"]["byVariant"]:
                stats["last24Hours"]["byVariant"][variant] = 0
            stats["last24Hours"]["byVariant"][variant] += 1
    
    # Calculate statistics for each variant
    for variant, sales in sales_by_variant.items():
        # Sort sales by date (newest first)
        sales.sort(key=lambda x: x["date"], reverse=True)
        
        # Calculate average price
        prices = [sale["price"] for sale in sales]
        avg_price = mean(prices) if prices else 0
        median_price = median(prices) if prices else 0
        
        # Calculate price trend
        if len(sales) >= 5:
            # Compare average of 5 most recent sales to 5 sales before that
            recent_avg = mean([sale["price"] for sale in sales[:5]])
            previous_avg = mean([sale["price"] for sale in sales[5:10]]) if len(sales) >= 10 else recent_avg
            
            if previous_avg > 0:
                percent_change = ((recent_avg - previous_avg) / previous_avg) * 100
            else:
                percent_change = 0
                
            # Determine trend
            if percent_change > 5:
                trend = "increasing"
            elif percent_change < -5:
                trend = "decreasing"
            else:
                trend = "stable"
        else:
            trend = "insufficient data"
            percent_change = 0
        
        # Store variant statistics
        stats["byVariant"][variant] = {
            "count": len(sales),
            "averagePrice": round(avg_price, 2),
            "medianPrice": round(median_price, 2),
            "priceTrend": trend,
            "percentChange": round(percent_change, 2) if isinstance(percent_change, float) else 0,
            "isIncreasing": trend == "increasing",
            "isDecreasing": trend == "decreasing",
            "highestPrice": round(max(prices), 2) if prices else 0,
            "lowestPrice": round(min(prices), 2) if prices else 0
        }
    
    # Calculate overall price trend (based on the most common variant or all data if no dominant variant)
    if sales_by_variant:
        # Find the variant with the most sales
        dominant_variant = max(sales_by_variant.keys(), key=lambda k: len(sales_by_variant[k]))
        
        # Use the dominant variant's trend for the overall trend
        if dominant_variant in stats["byVariant"]:
            stats["priceAnalysis"]["trend"] = stats["byVariant"][dominant_variant]["priceTrend"]
            stats["priceAnalysis"]["percentChange"] = stats["byVariant"][dominant_variant]["percentChange"]
            
            # Add explicit boolean flags for price direction
            stats["priceAnalysis"]["isIncreasing"] = stats["byVariant"][dominant_variant]["priceTrend"] == "increasing"
            stats["priceAnalysis"]["isDecreasing"] = stats["byVariant"][dominant_variant]["priceTrend"] == "decreasing"
    
    return stats

def fetch_sales_data(product_id, max_results=100):
    """
    Fetch sales data for a product from TCGPlayer API.
    
    Args:
        product_id: The TCGPlayer product ID
        max_results: Maximum number of results to fetch (default: 100)
        
    Returns:
        dict: Sales data response or None if failed
    """
    # Add the mpfev query parameter that's required
    url = f"https://mpapi.tcgplayer.com/v2/product/{product_id}/latestsales?mpfev=3442"
    
    # Mimic a real browser with proper headers
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
        "content-type": "application/json",
        "origin": "https://www.tcgplayer.com",
        "referer": "https://www.tcgplayer.com/",
        "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    
    # Get maximum results per request (API limit is 100)
    limit_per_request = min(100, max_results)
    
    # Initialize variables for pagination
    all_sales_data = []
    offset = 0
    total_results = None
    has_more_pages = True
    combined_response = None
    
    # For single product mode, try direct request first
    use_direct = len(sys.argv) > 1
    
    # Function to make a request with pagination
    def make_paginated_request(proxy=None):
        nonlocal offset, all_sales_data, total_results, has_more_pages, combined_response
        
        # Payload with limit and offset for pagination
        current_payload = {
            "limit": limit_per_request,
            "offset": offset
        }
        
        try:
            if proxy:
                response = requests.post(url, headers=headers, json=current_payload, proxies=proxy, timeout=10)
            else:
                response = requests.post(url, headers=headers, json=current_payload, timeout=10)
            
            if response.status_code == 429 and proxy:
                mark_proxy_rate_limited(proxy)
                return False  # Try again with a different proxy
            
            response.raise_for_status()
            page_data = response.json()
            
            if not page_data or not page_data.get('data'):
                return None
            
            # Store the first response structure to return later
            if combined_response is None:
                combined_response = page_data.copy()
            
            # Add the current page's data to our collection
            all_sales_data.extend(page_data.get('data', []))
            
            # Update total_results if not set yet
            if total_results is None and 'totalResults' in page_data:
                total_results = page_data['totalResults']
            
            # Check if there are more pages
            has_more_pages = page_data.get('nextPage') == "Yes"
            
            # Update offset for next page
            if has_more_pages:
                offset += len(page_data.get('data', []))
            
            return True
            
        except requests.exceptions.HTTPError as e:
            if proxy and e.response.status_code == 403:
                mark_proxy_error(proxy)
            return False
        except (requests.exceptions.ProxyError, requests.exceptions.ConnectTimeout):
            if proxy:
                mark_proxy_error(proxy)
            return False
        except Exception:
            if proxy:
                mark_proxy_error(proxy)
            return False
    
    # Try direct request first for single product mode
    if use_direct:
        while has_more_pages and (max_results <= 0 or len(all_sales_data) < max_results):
            success = make_paginated_request()
            if not success:
                break  # If direct request fails, fall through to proxy
            
            # If we've reached max_results or there are no more pages, stop
            if not has_more_pages or (max_results > 0 and len(all_sales_data) >= max_results):
                break
        
        # If we successfully got data directly, return it
        if combined_response and all_sales_data:
            # Update the combined response with all collected data
            combined_response['data'] = all_sales_data[:max_results] if max_results > 0 else all_sales_data
            combined_response['resultCount'] = len(combined_response['data'])
            combined_response['totalResults'] = total_results if total_results is not None else len(all_sales_data)
            combined_response['nextPage'] = "Yes" if has_more_pages and (max_results <= 0 or len(all_sales_data) < max_results) else ""
            combined_response['previousPage'] = ""  # Reset this as we're returning all pages
            return combined_response
    
    # Reset for proxy attempt
    all_sales_data = []
    offset = 0
    total_results = None
    has_more_pages = True
    combined_response = None
    
    # Use proxy for batch mode or if direct request failed
    while has_more_pages and (max_results <= 0 or len(all_sales_data) < max_results):
        success = False
        
        for retry in range(MAX_RETRIES):
            proxy = get_available_proxy()
            if not proxy:
                time.sleep(1)
                continue
                
            success = make_paginated_request(proxy)
            if success:
                break  # Successfully got data with this proxy
        
        # If we couldn't get data after all retries, stop
        if not success:
            break
        
        # If we've reached max_results or there are no more pages, stop
        if not has_more_pages or (max_results > 0 and len(all_sales_data) >= max_results):
            break
    
    # If we got any data, return it
    if combined_response and all_sales_data:
        # Update the combined response with all collected data
        combined_response['data'] = all_sales_data[:max_results] if max_results > 0 else all_sales_data
        combined_response['resultCount'] = len(combined_response['data'])
        combined_response['totalResults'] = total_results if total_results is not None else len(all_sales_data)
        combined_response['nextPage'] = "Yes" if has_more_pages and (max_results <= 0 or len(all_sales_data) < max_results) else ""
        combined_response['previousPage'] = ""  # Reset this as we're returning all pages
        return combined_response
    
    return None

def worker(task_queue, db, worker_id, progress_bar):
    sales_collection = db[SALES_COLLECTION]
    catalog_collection = db[CATALOG_COLLECTION]
    
    while True:
        try:
            item = task_queue.get(timeout=30)
            if item is None:
                task_queue.task_done()
                break
                
            product_id = item["productId"]
            
            # Fetch sales data with increased limit (up to 100 results)
            sales_data = fetch_sales_data(product_id, max_results=100)
            
            if sales_data and sales_data['data']:
                # Process sales data to calculate statistics
                sales_stats = calculate_sales_statistics(sales_data['data'])
                
                result = {
                    "productId": product_id,
                    "name": item.get("name"),
                    "expansionName": item.get("expansionName"),
                    "gameName": item.get("gameName"),
                    "variant": item.get("variant"),
                    "isSealed": item.get("isSealed", False),
                    "isSingle": item.get("isSingle", False),
                    "salesData": sales_data,
                    "salesStatistics": sales_stats,
                    "lastUpdated": datetime.now()
                }
                
                try:
                    sales_collection.update_one(
                        {"productId": product_id},
                        {"$set": result},
                        upsert=True
                    )
                    # Removed catalog update as per requirement - only write to saleshistories collection
                    # catalog_collection.update_one(
                    #     {"productId": product_id},
                    #     {"$set": {"history": datetime.now()}}
                    # )
                except Exception:
                    pass
            
            progress_bar.update(1)
            task_queue.task_done()
            
        except queue.Empty:
            break
        except Exception:
            if task_queue.unfinished_tasks > 0:
                task_queue.task_done()

def connect_to_mongodb():
    try:
        client = MongoClient(MONGODB_URI, serverSelectionTimeoutMS=5000)
        client.server_info()
        print("Successfully connected to MongoDB")
        return client[MONGODB_DB]
    except Exception:
        print("Failed to connect to MongoDB")
        return None

def process_category(db, category_id):
    catalog_collection = db[CATALOG_COLLECTION]
    
    # Simplified query that gets all products in the category
    query = {
        "categoryId": category_id
    }
    
    total_items = catalog_collection.count_documents(query)
    print(f"Category {category_id}: Found {total_items} products")
    
    if total_items == 0:
        return True
    
    progress_bar = tqdm(total=total_items, desc=f"Category {category_id}")
    
    # Use more threads for faster processing
    batch_threads = min(NUM_THREADS, 100)  # Use up to 100 threads for speed
    
    task_queue = queue.Queue()
    threads = []
    
    # Start worker threads
    for i in range(batch_threads):
        thread = threading.Thread(
            target=worker,
            args=(task_queue, db, i, progress_bar)
        )
        thread.daemon = True
        thread.start()
        threads.append(thread)
    
    # Queue items
    try:
        for item in catalog_collection.find(query):
            task_queue.put(item)
    except Exception:
        return False
    
    # Add sentinel values to stop workers
    for _ in range(batch_threads):
        task_queue.put(None)
    
    # Wait for completion
    task_queue.join()
    for thread in threads:
        thread.join()
    
    progress_bar.close()
    print(f"Finished processing category {category_id}")
    return True

def process_category_thread(db, category_id):
    """Process a category in its own thread"""
    try:
        process_category(db, category_id)
    except Exception as e:
        print(f"Error processing category {category_id}: {str(e)}")

def process_single_product(db, product_id, max_results=100):
    """
    Process a single product by ID instead of an entire category
    
    Args:
        db: MongoDB database connection
        product_id: TCGPlayer product ID
        max_results: Maximum number of sales results to fetch (default: 100)
    """
    catalog_collection = db[CATALOG_COLLECTION]
    
    # Find the product in the catalog
    product = catalog_collection.find_one({"productId": product_id})
    if not product:
        print(f"Product ID {product_id} not found in catalog")
        return False
    
    print(f"Found product: {product.get('name', 'Unknown')} (ID: {product_id})")
    
    # Create a single-item queue and process it
    task_queue = queue.Queue()
    progress_bar = tqdm(total=1, desc=f"Product {product_id}")
    
    # Start a single worker thread
    thread = threading.Thread(
        target=worker,
        args=(task_queue, db, 0, progress_bar)
    )
    thread.daemon = True
    thread.start()
    
    # Add the product to the queue
    task_queue.put(product)
    
    # Add sentinel value to stop worker
    task_queue.put(None)
    
    # Wait for completion
    task_queue.join()
    thread.join()
    
    progress_bar.close()
    print(f"Finished processing product {product_id}")
    return True

def main():
    global proxies
    
    print("Starting TCGPlayer sales history fetcher")
    
    # Check if a product ID was provided as a command-line argument
    specific_product_id = None
    if len(sys.argv) > 1:
        try:
            specific_product_id = int(sys.argv[1])
            print(f"Running for specific product ID: {specific_product_id}")
        except ValueError:
            print(f"Invalid product ID: {sys.argv[1]}")
            return
    
    # Always load proxies, even for single product mode as fallback
    print("Loading proxies")
    proxies = load_proxies()
    if not proxies:
        print("No proxies available. This may affect batch processing.")
        
        # For single product mode, we can continue without proxies
        # For batch mode, we should retry
        if not specific_product_id:
            print("Cannot run batch mode without proxies. Exiting.")
            return
    
    print("Connecting to MongoDB")
    db = connect_to_mongodb()
    if db is None:
        print("Failed to connect to MongoDB. Exiting.")
        return
    
    # If a specific product ID was provided, process only that product
    if specific_product_id:
        print(f"Processing specific product ID: {specific_product_id}")
        # Process single product with maximum results (100)
        success = process_single_product(db, specific_product_id, max_results=100)
        if success:
            print(f"Successfully processed product ID: {specific_product_id}")
        else:
            print(f"Failed to process product ID: {specific_product_id}")
        return  # Exit after processing the specific product
    
    # Batch processing mode - process one category at a time
    while True:
        try:
            # Refresh proxies periodically for batch mode
            if not proxies or len(proxies) < 10:  # If we have fewer than 10 proxies
                print("Refreshing proxies")
                proxies = load_proxies()
                if not proxies:
                    print("No proxies available for batch processing. Retrying in 60 seconds...")
                    time.sleep(60)
                    continue
            
            # Process categories in pairs, running 2 at a time
            print("Processing categories in pairs (2 at a time)")
            
            # Process categories in pairs
            for i in range(0, len(CATEGORIES), 2):
                category_threads = []
                
                # Get the current pair of categories (or just one if we're at the end)
                current_categories = CATEGORIES[i:i+2]
                
                print(f"Processing categories: {current_categories}")
                
                # Start a thread for each category in the current pair
                for category_id in current_categories:
                    print(f"Starting thread for category {category_id}")
                    thread = threading.Thread(
                        target=process_category_thread,
                        args=(db, category_id)
                    )
                    thread.daemon = True
                    thread.start()
                    category_threads.append(thread)
                
                # Wait for both categories to complete before moving to the next pair
                for thread in category_threads:
                    thread.join()
                
                print(f"Finished processing categories: {current_categories}")
                
                # Brief pause between category pairs
                time.sleep(5)
            
            print(f"All categories processed. Waiting {WAIT_TIME/3600} hours before next run...")
            time.sleep(WAIT_TIME)
                
        except Exception as e:
            print(f"Error in main loop: {str(e)}")
            time.sleep(60)

if __name__ == "__main__":
    main()
